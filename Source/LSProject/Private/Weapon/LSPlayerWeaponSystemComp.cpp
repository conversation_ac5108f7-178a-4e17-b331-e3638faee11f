#include "Weapon/LSPlayerWeaponSystemComp.h"
#include "Character/LSPlayerCharacter.h"
#include "Weapon/LSWeaponBase.h"
#include "Component/LSInventoryComp.h"
#include "Controller/LSPlayerController.h"

ULSPlayerWeaponSystemComp::ULSPlayerWeaponSystemComp()
{
	PrimaryComponentTick.bCanEverTick = false;

	CurrentWeapon = nullptr;
	IsArmedWeapon = false;
}

void ULSPlayerWeaponSystemComp::BeginPlay()
{
	Super::BeginPlay();

	OwnerCharacter = Cast<ALSPlayerCharacter>(GetOwner());
}

void ULSPlayerWeaponSystemComp::EquipWeapon()
{
	//TODO: 빈 WeaponClass 배열에 대한 로그 추가
	// 디버깅을 위해 왜 장착이 실패했는지 명확히 알 수 있도록 로그 출력
	if (WeaponClass.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponClass array is empty in %s"), *GetName());
		return;
	}

	//TODO: 기존 무기 해제 로직 개선 필요
	// Destroy() 호출 시 즉시 삭제되지 않을 수 있으므로
	// 더 안전한 해제 방법 고려 (DetachFromActor 후 Destroy)
	if (CurrentWeapon)
	{
		CurrentWeapon->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		CurrentWeapon->Destroy();
		CurrentWeapon = nullptr;
	}

	//TODO: OwnerCharacter 유효성 검사 강화
	if (!OwnerCharacter)
	{
		UE_LOG(LogTemp, Error, TEXT("OwnerCharacter is null in %s::EquipWeapon()"), *GetName());
		return;
	}

	const int32 Index = static_cast<int32>(OwnerCharacter->GetCurrentWeapon()) - 1;

	//TODO: 인덱스 유효성 검사 개선
	// 음수 인덱스에 대한 추가 검사 필요 (ECurrentWeapon::None의 경우)
	if (Index < 0)
	{
		UE_LOG(LogTemp, Log, TEXT("No weapon selected (ECurrentWeapon::None)"));
		return;
	}

	if (WeaponClass.IsValidIndex(Index))
	{
		//TODO: SpawnActor 실패 처리 추가
		// SpawnActor가 실패할 수 있으므로 반환값 검증 필요
		CurrentWeapon = Cast<ALSWeaponBase>(GetWorld()->SpawnActor(WeaponClass[Index]));
		if (!CurrentWeapon)
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to spawn weapon at index %d"), Index);
			return;
		}

		CurrentWeapon->SetOwner(OwnerCharacter);

		//TODO: 메시 컴포넌트 유효성 검사 추가
		if (USkeletalMeshComponent* Mesh = OwnerCharacter->GetMesh())
		{
			//TODO: 소켓 존재 여부 확인 필요
			// "RightWeapon" 소켓이 존재하지 않을 경우 AttachToComponent 실패
			if (Mesh->DoesSocketExist(TEXT("RightWeapon")))
			{
				IsArmedWeapon = CurrentWeapon->AttachToComponent(
					Mesh, FAttachmentTransformRules::SnapToTargetNotIncludingScale,
					TEXT("RightWeapon"));

				if (!IsArmedWeapon)
				{
					UE_LOG(LogTemp, Error, TEXT("Failed to attach weapon to RightWeapon socket"));
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("RightWeapon socket not found on character mesh"));
				IsArmedWeapon = false;
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Character mesh is null"));
			IsArmedWeapon = false;
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid weapon index: %d (array size: %d)"), Index, WeaponClass.Num());
	}
}

void ULSPlayerWeaponSystemComp::UnEquipWeapon()
{
	if (CurrentWeapon)
	{
		//TODO: 개발자 이름 주석 제거 필요
		// 코드에 개발자 이름을 남기는 것보다는 기능 설명이나 TODO 주석이 더 적절

		//TODO: OwnerCharacter 유효성 검사 추가
		if (!OwnerCharacter)
		{
			UE_LOG(LogTemp, Error, TEXT("OwnerCharacter is null in UnEquipWeapon"));
			return;
		}

		ULSInventoryComp* InvenComp = OwnerCharacter->FindComponentByClass<ULSInventoryComp>();
		if (!InvenComp)
		{
			//TODO: 인벤토리 컴포넌트가 없을 때의 처리 개선
			// 단순히 return하지 말고 로그를 남기고 무기는 해제하는 것이 좋을 수 있음
			UE_LOG(LogTemp, Warning, TEXT("InventoryComp not found, weapon will be unequipped without saving ammo"));
		}
		else
		{
			//TODO: 탄약 저장 로직 검증 필요
			// GetCurrentAmmo()가 음수이거나 비정상적인 값을 반환할 수 있으므로 검증 추가
			int32 CurrentAmmo = CurrentWeapon->GetCurrentAmmo();
			if (CurrentAmmo > 0)
			{
				InvenComp->AddAmmoToInven(CurrentAmmo);
			}
		}

		OwnerCharacter->SetCurrentWeapon(ECurrentWeapon::None);

		//TODO: 무기 해제 순서 개선
		// DetachFromActor를 먼저 호출한 후 Destroy하는 것이 더 안전
		CurrentWeapon->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		CurrentWeapon->Destroy();
		CurrentWeapon = nullptr;
		IsArmedWeapon = false;  // TODO: IsArmedWeapon 상태도 업데이트 필요
	}
	else
	{
		//TODO: 이미 무기가 없는 상태에서 UnEquip 호출 시 로그 추가
		UE_LOG(LogTemp, Log, TEXT("No weapon to unequip"));
	}
}
