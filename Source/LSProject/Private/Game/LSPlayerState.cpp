// Fill out your copyright notice in the Description page of Project Settings.


#include "Game/LSPlayerState.h"

#include "Kismet/GameplayStatics.h"

ALSPlayerState::ALSPlayerState()
{
	EmptySound = nullptr;

	//TODO: 초기 코인 수량을 설정 가능한 값으로 변경
	// 500이라는 값을 하드코딩하지 말고 게임 밸런스에 따라 조정 가능하도록
	// UPROPERTY(EditAnywhere)로 노출하거나 게임 설정에서 가져오도록 개선
	Coin = 500;
}

void ALSPlayerState::AddZombieKill(int32 Delta)
{
	//TODO: Delta 값 유효성 검사 추가
	// 음수 Delta에 대한 처리나 최대값 제한 고려
	if (Delta < 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Negative delta (%d) passed to AddZombieKill"), Delta);
	}

	ZombieNum = FMath::Max(0, ZombieNum + Delta);

	//TODO: 킬 수 변경 이벤트 발생 고려
	// UI 업데이트나 업적 체크를 위한 델리게이트 호출
}

bool ALSPlayerState::AddCoin(int32 NewCoin)
{
	//TODO: 함수명과 로직 불일치 수정 필요
	// 함수명은 AddCoin인데 음수 값으로 코인을 차감하는 로직도 포함되어 있음
	// AddCoin과 RemoveCoin을 분리하거나 함수명을 ModifyCoin으로 변경 고려

	if (Coin + NewCoin >= 0)
	{
		Coin += NewCoin;

		//TODO: 코인 변경 이벤트 발생 고려
		// UI 업데이트를 위한 델리게이트 호출 필요

		return true;
	}

	//TODO: 코인 부족 시 처리 개선
	// 단순히 사운드만 재생하는 것보다는 UI 알림이나 로그도 함께 처리
	if (EmptySound)
	{
		UGameplayStatics::PlaySound2D(this, EmptySound);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("EmptySound not set, cannot play insufficient coin sound"));
	}

	UE_LOG(LogTemp, Log, TEXT("Insufficient coins: Current=%d, Required=%d"), Coin, -NewCoin);
	return false;
}
