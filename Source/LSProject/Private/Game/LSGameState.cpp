#include "Game/LSGameState.h"
#include "Kismet/GameplayStatics.h"
#include "System/LSDayNightController.h"
#include "Controller/LSPlayerController.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "EngineUtils.h"                
#include "Enemy/LSEnemySpawnVolume.h"
#include "Game/LSPlayerState.h"
#include "Character/LSPlayerCharacter.h"
#include "Weapon/LSPlayerWeaponSystemComp.h"
#include "Weapon/LSWeaponBase.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Game/LSGameMode.h"

void ALSGameState::BeginPlay()
{
	Super::BeginPlay();

	//TODO: DayNightController 검색 실패 시 처리 추가
	// GetActorOfClass가 nullptr을 반환할 수 있으므로 유효성 검사 및 에러 처리 필요
	DayNightCtrl = Cast<ALSDayNightController>(
		UGameplayStatics::GetActorOfClass(GetWorld(), ALSDayNightController::StaticClass())
	);
	if (!DayNightCtrl)
	{
		UE_LOG(LogTemp, Warning, TEXT("DayNightController not found in the level"));
	}

	TryRegisterSpawnVolumes();

	//TODO: GameMode 접근 실패 시 처리 개선
	// GetAuthGameMode가 실패할 수 있으므로 로그 추가 권장
	if (ALSGameMode* GM = GetWorld()->GetAuthGameMode<ALSGameMode>())
	{
		//TODO: BGM 설정을 더 체계적으로 관리
		// 각 BGM이 null인지 확인하고 로그를 남기는 것이 디버깅에 도움됨
		if (GM->StartMapBGM) StartMapBGM = GM->StartMapBGM;
		else UE_LOG(LogTemp, Warning, TEXT("StartMapBGM not set in GameMode"));

		if (GM->DayBGM) DayBGM = GM->DayBGM;
		else UE_LOG(LogTemp, Warning, TEXT("DayBGM not set in GameMode"));

		if (GM->NightBGM) NightBGM = GM->NightBGM;
		else UE_LOG(LogTemp, Warning, TEXT("NightBGM not set in GameMode"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to get GameMode"));
	}

	//TODO: 레벨별 초기화를 더 확장 가능하게 개선
	// 현재는 두 개 레벨만 처리하는데, 맵이 추가될 때마다 수정해야 함
	// 맵별 설정을 데이터 테이블이나 설정 파일로 관리하는 것 고려
	const FString LevelName = UGameplayStatics::GetCurrentLevelName(GetWorld(), true);
	if (LevelName.Equals(TEXT("LSStartMap"), ESearchCase::IgnoreCase))
	{
		InitStartMapBGM();
	}
	else if (LevelName.Equals(TEXT("LSMainMap"), ESearchCase::IgnoreCase))
	{
		InitMainMapBGM();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Unknown level: %s"), *LevelName);
	}

	//TODO: HUD 업데이트 주기를 설정 가능한 변수로 변경
	// 0.2f 하드코딩 값을 UPROPERTY로 노출하여 조정 가능하도록 개선
	GetWorldTimerManager().SetTimer(
		HUDUpdateTimerHandle, this, &ALSGameState::UpdateHUD, 0.2f, true, 0.0f
	);

	UpdateHUD();
}

ALSGameState::ALSGameState()
{
	bIsCharacterOverlappedWithDoor=false;
	bIsDay=true;
	
	USceneComponent* Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
	RootComponent = Root;

	BGMComp = CreateDefaultSubobject<UAudioComponent>(TEXT("BGMComp"));
	BGMComp->SetupAttachment(RootComponent);
	BGMComp->bAutoActivate = false;
	BGMComp->bAllowSpatialization = false;
	BGMComp->bIsUISound = false;
	
}

bool ALSGameState::bGetCanOpenShopUI()
{
	if (bIsCharacterOverlappedWithDoor && bIsDay)
	{
		return true;
	}
	
	return false;
}


void ALSGameState::UpdateHUD()
{
	if (!DayNightCtrl) return;
	if (HasAuthority())
	{
		const bool bIsDayNow = DayNightCtrl->IsDayPhase();
		const int32 DayNow = DayNightCtrl->GetCurrentDay();
		if (bIsDayNow != bPrevIsDay || DayNow != PrevDay)
		{
			if (!bIsDayNow)   StartNightWave(DayNow); // 밤 시작
			else              EndWave();           // 낮 시작(스폰 정지)

			bPrevIsDay = bIsDayNow;
			PrevDay = DayNow;
		}
	}
	auto* PC = Cast<ALSPlayerController>(GetWorld()->GetFirstPlayerController());
	if (!PC) return;

	UUserWidget* HUD = PC->GetHUDWidget();
	if (!HUD) return;

	if (!DayTextBlock)
		DayTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("DayText")));
	if (!TimeTextBlock)
		TimeTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("TimeText")));
	if (!CoinTextBlock)
		CoinTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("CoinText")));
	if (!KillTextBlock)
		KillTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("KillText")));
	if (!ShopPressTextBlock)
		ShopPressTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("ShopPressText")));
	if (!BulletTextBlock)
		BulletTextBlock = Cast<UTextBlock>(HUD->GetWidgetFromName(TEXT("BulletTextBlock")));
	if (!DayTextBlock || !TimeTextBlock || !ShopPressTextBlock || !BulletTextBlock) return;

	//데이 텍스트 업데이트
	const int32 Day = DayNightCtrl->GetCurrentDay();
	DayTextBlock->SetText(FText::FromString(FString::Printf(TEXT("Day %d"), Day)));

	const int32 Secs = DayNightCtrl->GetRemainSecond();
	const int32 M = Secs / 60;
	const int32 S = Secs % 60;
	const FString Phase = DayNightCtrl->IsDayPhase() ? TEXT("DayTime") : TEXT("NightTime");
	TimeTextBlock->SetText(FText::FromString(FString::Printf(TEXT("%s %02d:%02d"), *Phase, M, S)));

	if (CoinTextBlock)
	{
		if (ALSPlayerState* PS = PC->GetPlayerState<ALSPlayerState>())
		{
			CoinTextBlock->SetText(FText::FromString(FString::Printf(TEXT("%d$"), PS->GetCoin())));
		}
		else
		{
			CoinTextBlock->SetText(FText::FromString(TEXT("0$")));
		}
	}
	int32 Kills = 0;
	if (KillTextBlock)
	{
		if (ALSPlayerState* PS = PC->GetPlayerState<ALSPlayerState>())
			Kills = PS->GetZombieNum();

		KillTextBlock->SetText(FText::FromString(FString::Printf(TEXT("Kills : %d / 100"), Kills)));
	}

	if (bGetCanOpenShopUI())
	{
		ShopPressTextBlock->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ShopPressTextBlock->SetVisibility((ESlateVisibility::Hidden));
	}

	const bool  bIsDayNow = DayNightCtrl->IsDayPhase();
	const int32 DayNow = DayNightCtrl->GetCurrentDay();
	if (!bLocalClearShown && Kills >= 80)
	{
		if (PC->IsLocalPlayerController())
		{
			PC->ShowGameClearWidget();
			bLocalClearShown = true;
		}
	}
	if ((bIsDayNow != bLocalPrevIsDay) || (DayNow != LocalPrevDay))
	{
		if (!bLocalClearShown && bIsDayNow && (LocalPrevDay == 5))
		{
			if (PC->IsLocalPlayerController())
			{
				PC->ShowGameClearWidget();
				bLocalClearShown = true;
			}
		}
		PlayBGM(bIsDayNow ? DayBGM : NightBGM, 1.2f, 0.6f);
		bLocalPrevIsDay = bIsDayNow;
		LocalPrevDay = DayNow;
	}

	
	if (BulletTextBlock)
	{
		if (ALSPlayerCharacter* Character = Cast<ALSPlayerCharacter>(PC->GetPawn()))
		{
			if (ULSPlayerWeaponSystemComp* WeaponComp = Character->FindComponentByClass<ULSPlayerWeaponSystemComp>())
			{
				int32 CurrentAmmo = 0;
				int32 MaxAmmo = 0;
				if (WeaponComp->CurrentWeapon)
				{
					CurrentAmmo = WeaponComp->CurrentWeapon->GetCurrentAmmo();
					MaxAmmo = WeaponComp->CurrentWeapon->GetMaxAmmo();
				}

				BulletTextBlock->SetText(FText::FromString(FString::Printf(TEXT("Bullet : %d / %d"), CurrentAmmo, MaxAmmo)));
			}
		}
	}
}
void ALSGameState::TryRegisterSpawnVolumes()
{
	if (SpawnVolumes.Num() > 0) return;

	for (TActorIterator<ALSEnemySpawnVolume> It(GetWorld()); It; ++It)
	{
		SpawnVolumes.Add(*It);
	}
}

void ALSGameState::StartNightWave(int32 Day)
{
	if (bWaveActive) return;

	bWaveActive = true;
	RemainingToSpawn = DaySpawnBudget.IsValidIndex(Day) ? DaySpawnBudget[Day] : 0;
	
	if (RemainingToSpawn > 0)
	{
		GetWorldTimerManager().SetTimer(
			SpawnTimerHandle, this, &ALSGameState::SpawnTick, SpawnInterval, true);
	}
}

void ALSGameState::EndWave()
{
	GetWorldTimerManager().ClearTimer(SpawnTimerHandle);
	bWaveActive = false;
	RemainingToSpawn = 0;
	DespawnRemainZombie();
}

void ALSGameState::SpawnTick()
{
	if (RemainingToSpawn <= 0)
	{
		GetWorldTimerManager().ClearTimer(SpawnTimerHandle);
		return;
	}

	TryRegisterSpawnVolumes();
	TArray<ALSEnemySpawnVolume*> Valid;
	for (auto& W : SpawnVolumes) if (W.IsValid()) Valid.Add(W.Get());
	if (Valid.Num() == 0)
	{
		GetWorldTimerManager().ClearTimer(SpawnTimerHandle);
		return;
	}

	ALSEnemySpawnVolume* V = Valid[FMath::RandRange(0, Valid.Num()-1)];
	const int32 CurrentDay = DayNightCtrl ? DayNightCtrl->GetCurrentDay() : PrevDay;
	V->SpawnEnemy(CurrentDay);

	--RemainingToSpawn;

	if (RemainingToSpawn <= 0)
	{
		GetWorldTimerManager().ClearTimer(SpawnTimerHandle);
	}
}


void ALSGameState::OnEnemyKilled()
{
	//TODO: 멀티플레이어 환경에서의 처리 고려
	// GetFirstPlayerController()는 첫 번째 플레이어만 반환하므로
	// 멀티플레이어에서는 실제 킬을 한 플레이어를 찾는 로직 필요
	if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
	{
		if (ALSPlayerState* PS = Cast<ALSPlayerState>(PC->PlayerState))
		{
			PS->AddZombieKill();

			//TODO: 코인 보상을 하드코딩하지 말고 설정 가능한 값으로 변경
			// 적 타입별로 다른 보상을 주거나, 게임 밸런스에 따라 조정 가능하도록 개선
			PS->AddCoin(50);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to get PlayerState in OnEnemyKilled"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No PlayerController found in OnEnemyKilled"));
	}

	//TODO: 적 처치 시 추가 이벤트 처리 고려
	// - 처치 이펙트 재생
	// - 업적/도전과제 확인
	// - 레벨업 체크 등

	UpdateHUD();
}
void ALSGameState::DespawnRemainZombie()
{
	if (!HasAuthority()) return;

	int32 Removed = 0;
	for (TActorIterator<ALSEnemy> It(GetWorld()); It; ++It)
	{
		ALSEnemy* Enemy = *It;
		if (!Enemy) continue;
		
		Enemy->GetWorldTimerManager().ClearAllTimersForObject(Enemy);

		Enemy->Destroy();
		++Removed;
	}

	AliveEnemies     = 0;
	RemainingToSpawn = 0;
}
void ALSGameState::PlayBGM(USoundBase* Sound, float FadeTime, float Volume)
{
	if (!BGMComp) return;

	if (Sound && Sound == CurrentBGMSound && BGMComp->IsPlaying())
		return;

	if (BGMComp->IsPlaying())
		BGMComp->FadeOut(FadeTime, 0.f);

	CurrentBGMSound = Sound;
	BGMComp->SetSound(Sound);
	if (Sound)
	{
		BGMComp->FadeIn(FadeTime, Volume);
		BGMComp->Play();
	}
}

void ALSGameState::InitStartMapBGM()
{
	PlayBGM(StartMapBGM, 3.0f, 0.5f); 
}

void ALSGameState::InitMainMapBGM()
{
	const bool bIsDayNow = DayNightCtrl ? DayNightCtrl->IsDayPhase() : true;
	PlayBGM(bIsDayNow ? DayBGM : NightBGM, 3.0f, 0.1f);
	bLocalPrevIsDay = bIsDayNow;
	LocalPrevDay = DayNightCtrl ? DayNightCtrl->GetCurrentDay() : LocalPrevDay;
}