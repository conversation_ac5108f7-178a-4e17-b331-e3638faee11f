#include "Component/LSCharacterStateComp.h"
#include "Character/LSPlayerCharacter.h"

ULSCharacterStateComp::ULSCharacterStateComp()
{
	PrimaryComponentTick.bCanEverTick = false;

	CurrentState = ECharacterState::Idle;
}

void ULSCharacterStateComp::BeginPlay()
{
	Super::BeginPlay();

	OwnerCharacter = Cast<ALSPlayerCharacter>(GetOwner());

	if (OwnerCharacter)
	{
		OwnerAnimInstance = OwnerCharacter->GetMesh()->GetAnimInstance();
		if (OwnerAnimInstance)
		{
			OwnerAnimInstance->OnMontageEnded.AddDynamic(this, &ULSCharacterStateComp::OnMontageEnded);
		}
	}
}

void ULSCharacterStateComp::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);

	if (OwnerAnimInstance)
	{
		OwnerAnimInstance->OnMontageEnded.RemoveDynamic(this, &ULSCharacterStateComp::OnMontageEnded);
	}
}

void ULSCharacterStateComp::OnMontageEnded(UAnimMontage* Montage, bool bInterrupted)
{
	//TODO: 중단된 몽타주에 대한 처리 고려
	// 중단된 몽타주도 상황에 따라 상태 변경이 필요할 수 있음
	// 예: 공격 중 피격으로 중단된 경우 등
	if (bInterrupted)
	{
		UE_LOG(LogTemp, Log, TEXT("Montage interrupted: %s"), Montage ? *Montage->GetName() : TEXT("Unknown"));
		return;
	}

	//TODO: OwnerCharacter 유효성 검사 추가
	if (!OwnerCharacter)
	{
		UE_LOG(LogTemp, Error, TEXT("OwnerCharacter is null in OnMontageEnded"));
		return;
	}

	//TODO: 체력 확인 로직 개선
	// IsNearlyZero보다는 <= 0.0f 비교가 더 명확할 수 있음
	// 또한 체력이 0인 상태에서도 Die 상태가 아닐 수 있으므로 현재 상태 확인 필요
	if (OwnerCharacter->GetCurrentHealth() <= 0.0f)
	{
		SetState(ECharacterState::Die);
	}
	else
	{
		//TODO: 무조건 Idle로 전환하는 것보다 이전 상태나 몽타주 타입에 따른 처리 고려
		// 예: 재장전 몽타주 종료 시 Idle이 아닌 다른 상태로 전환 필요할 수 있음
		SetState(ECharacterState::Idle);
	}

	//TODO: 로그 레벨 조정 및 더 유용한 정보 포함
	// Warning보다는 Log 레벨이 적절하고, 어떤 몽타주가 끝났는지 정보 포함
	UE_LOG(LogTemp, Log, TEXT("Montage ended: %s, New state: %d"),
		Montage ? *Montage->GetName() : TEXT("Unknown"),
		static_cast<int32>(CurrentState));
}

bool ULSCharacterStateComp::CanMove() const
{
	return CurrentState != ECharacterState::Die;
}

bool ULSCharacterStateComp::CanJump() const
{
	return CurrentState != ECharacterState::Die;
}

bool ULSCharacterStateComp::CanEquip() const
{
	switch (CurrentState)
	{
	case ECharacterState::Fire:
		return false;
	case ECharacterState::Reload:
		return false;
	case ECharacterState::Die:
		return false;
	default:
		return true;
	}
}

bool ULSCharacterStateComp::CanFire() const
{
	switch (CurrentState)
	{
	case ECharacterState::Equip:
		return false;
	case ECharacterState::Reload:
		return false;
	case ECharacterState::Die:
		return false;
	default:
		return true;
	}
}

bool ULSCharacterStateComp::CanReload() const
{
	switch (CurrentState)
	{
	case ECharacterState::Equip:
		return false;
	case ECharacterState::Fire:
		return false;
	case ECharacterState::Reload:
		return false;
	case ECharacterState::Die:
		return false;
	default:
		return true;
	}
}

bool ULSCharacterStateComp::CanDie() const
{
	switch (CurrentState)
	{
	case ECharacterState::Die:
		return false;
	default:
		return true;
	}
}

ECharacterState ULSCharacterStateComp::GetCurrentState() const
{
	return CurrentState;
}

void ULSCharacterStateComp::SetState(ECharacterState NewState)
{
	if (CurrentState == NewState) return;

	CurrentState = NewState;
	UE_LOG(LogTemp, Warning, TEXT("Set State : %d"), CurrentState);
}

void ULSCharacterStateComp::StopCurrentMontage()
{
	if (!OwnerCharacter) return;

	if (UAnimInstance* AnimInstance = OwnerCharacter->GetMesh()->GetAnimInstance())
	{
		if (AnimInstance->IsAnyMontagePlaying())
		{
			AnimInstance->Montage_Stop(0.25f);
		}
	}
}
