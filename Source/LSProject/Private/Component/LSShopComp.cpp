// Fill out your copyright notice in the Description page of Project Settings.

#include "Component//LSShopComp.h"
#include "Controller/LSPlayerController.h"
#include "Game/LSPlayerState.h"
#include "DataTable/LSShopItemRow.h"
#include "Engine/DataTable.h"
#include "Component/LSInventoryComp.h"
#include "Character/LSPlayerCharacter.h"
#include "Kismet/GameplayStatics.h"

ULSShopComp::ULSShopComp()
{
	//TODO: Tick 활성화 이유 확인 필요
	// 상점 컴포넌트에서 Tick이 필요한 이유가 명확하지 않음
	// 불필요한 성능 오버헤드를 피하기 위해 false로 설정 권장
	PrimaryComponentTick.bCanEverTick = true;

	//TODO: DataTable을 컴포넌트에서 생성하는 것은 비권장
	// DataTable은 에디터에서 생성하고 UPROPERTY로 할당하는 것이 일반적
	// CreateDefaultSubobject 대신 nullptr로 초기화하고 블루프린트에서 설정하도록 변경 권장
	ShopItemData = CreateDefaultSubobject<UDataTable>(TEXT("ShopItemData"));

	Inventory = nullptr;
}


void ULSShopComp::BeginPlay()
{
	Super::BeginPlay();

	//TODO: 컴포넌트 검색 로직 개선 필요
	// BeginPlay에서 매번 검색하는 것보다는 생성자에서 참조를 설정하거나
	// 지연 초기화 방식 사용 고려
	if (AActor* Owner = GetOwner())
	{
		if (ALSPlayerCharacter* Player = Cast<ALSPlayerCharacter>(Owner))
		{
			ULSInventoryComp* InvenComp = Player->FindComponentByClass<ULSInventoryComp>();
			if (InvenComp)
			{
				Inventory = InvenComp;
			}
			else
			{
				//TODO: 인벤토리 컴포넌트를 찾지 못했을 때 로그 추가
				UE_LOG(LogTemp, Warning, TEXT("InventoryComp not found on %s"), *Player->GetName());
			}
		}
		else
		{
			//TODO: 플레이어 캐릭터가 아닌 액터에 상점 컴포넌트가 붙었을 때 처리
			UE_LOG(LogTemp, Warning, TEXT("ShopComp attached to non-player actor: %s"), *Owner->GetName());
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("ShopComp has no owner"));
	}
}

void ULSShopComp::BuyItem(const FName& ItemName)
{
	if (!ShopItemData)	return;
	
	if (ALSPlayerController* PC=Cast<ALSPlayerController>(GetWorld()->GetFirstPlayerController()))
	{
		if (ALSPlayerState* PS=PC->GetPlayerState<ALSPlayerState>())
		{
			const FString Context(TEXT("Get Item"));
			const FLSShopItemRow* Row=ShopItemData->FindRow<FLSShopItemRow>(ItemName,Context,true);
			if (Row)
			{
				int32 ItemPrice=Row->Price;

				if (PS->AddCoin(-ItemPrice))
				{
					Inventory->AddToInven(ItemName,Row->Number);
					UGameplayStatics::PlaySound2D(this, ClickSound);
				}
				else
				{
					OnShopNotEnoughMoney.Broadcast();
				}
				
			}
			
		}
	}
}
