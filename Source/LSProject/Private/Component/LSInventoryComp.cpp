
#include "Component/LSInventoryComp.h"
#include "Character/LSPlayerCharacter.h"
#include "Weapon/LSPlayerWeaponSystemComp.h"
#include "Controller/LSPlayerController.h"
#include "Widget/LSInventoryWidget.h"

ULSInventoryComp::ULSInventoryComp()
{
	PrimaryComponentTick.bCanEverTick = false;

	//TODO: 기본 무기명을 상수로 정의 권장
	// "None" 문자열을 여러 곳에서 사용하므로 static const FName으로 정의하여 일관성 확보
	MyWeaponName = TEXT("None");
}

void ULSInventoryComp::BeginPlay()
{
	Super::BeginPlay();

	//TODO: 초기 인벤토리 설정 로직 추가 고려
	// 게임 시작 시 기본 아이템들을 추가하거나 저장된 인벤토리 데이터 로드
}

void ULSInventoryComp::AddToInven(const FName& Input, int32 Amount)
{
	//TODO: 음수 Amount 처리 필요
	// 음수 값이 들어올 경우 아이템 제거로 처리할지, 에러로 처리할지 명확히 해야 함
	if (Amount <= 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid amount (%d) for item %s"), Amount, *Input.ToString());
		return;
	}

	int32& NewValue = MyItems.FindOrAdd(Input);
	NewValue += Amount;

	//TODO: 최대 소지 가능 수량 제한 고려
	// 아이템별로 최대 소지 가능 수량을 설정하여 무한 증가 방지

	//TODO: 인벤토리 변경 이벤트 발생
	// UI 업데이트를 위한 델리게이트 호출 필요
}

int32 ULSInventoryComp::CountItem(const FName& Input)
{
	//TODO: Contains 체크 후 다시 [] 연산자 사용하는 것보다
	// Find를 사용하여 한 번의 검색으로 처리하는 것이 더 효율적
	if (const int32* ItemCount = MyItems.Find(Input))
	{
		return *ItemCount;
	}

	return 0;
}

void ULSInventoryComp::Equip(const FName& Input)
{
	if (!MyItems.Contains(Input))	return;

	if (MyItems[Input]<=0)	return;
	MyItems[Input]--;

	MyWeaponName=Input;

	AActor* Owner = GetOwner();
	if (!Owner) return;

	EquipWeapon();
}

void ULSInventoryComp::Unequip()
{
	if (int32* ExistingValue = MyItems.Find(MyWeaponName))
	{
		(*ExistingValue)++;
	}
	else
	{
		MyItems.Add(MyWeaponName, 1);
	}
	
	AActor* Owner = GetOwner();
	if (!Owner) return;

	ALSPlayerCharacter* Character=Cast<ALSPlayerCharacter>(Owner);
	if (!Character) return;

	ULSPlayerWeaponSystemComp* OtherComp = Character->FindComponentByClass<ULSPlayerWeaponSystemComp>();
	if (!OtherComp) return;
	
	//애니메이션
	EquipWeapon();

	//무기 삭제
	OtherComp->UnEquipWeapon();

	//무기 destroy 후 조정
	MyWeaponName=TEXT("None");
	
	//UI Update
	ALSPlayerController* PC=Cast<ALSPlayerController>(Character->GetController());
	if (!PC)	return;
	if (!PC->GetInvenWidget())	return;
	PC->GetInvenWidget()->OnUpdateInvenUI.Broadcast();
	
}

void ULSInventoryComp::ChangeWeaponSlot(const FName& NewWeapon)
{
	Unequip();
	MyWeaponName=NewWeapon;
	Equip(MyWeaponName);
}

bool ULSInventoryComp::HasAmmo()
{
	FString AmmoStr=MyWeaponName.ToString();
	AmmoStr.Append("Ammo");
	FName Ammo=FName(*AmmoStr);

	if (MyItems.Find(Ammo) && MyItems[Ammo]>0)
	{
		return true;
	}

	return false;
}

int ULSInventoryComp::RequiredAmmo(int32 RequiredAmmo)
{
	FString AmmoStr=MyWeaponName.ToString();
	AmmoStr.Append("Ammo");
	FName AmmoName=FName(*AmmoStr);

	if (MyItems.Find(AmmoName))
	{
		if (MyItems[AmmoName]>=RequiredAmmo)
		{
			MyItems[AmmoName]-=RequiredAmmo;
			return	RequiredAmmo;
		}
		else
		{
			int32 Available = MyItems[AmmoName];
			MyItems[AmmoName]=0;
			return Available;
		}
	}

	return 0;
}

void ULSInventoryComp::AddAmmoToInven(int32 RequiredAmmo)
{
	//TODO: 함수명과 매개변수명 불일치 수정 필요
	// 함수명은 AddAmmoToInven인데 매개변수명이 RequiredAmmo로 되어 있어 혼란 야기
	// AddedAmmo 또는 AmmoAmount 등으로 변경 권장

	//TODO: 음수 탄약 처리 필요
	if (RequiredAmmo <= 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid ammo amount: %d"), RequiredAmmo);
		return;
	}

	//TODO: 문자열 조작 최적화 필요
	// 매번 문자열을 생성하는 것보다 캐싱하거나 다른 방식 고려
	FString AmmoStr = MyWeaponName.ToString();
	AmmoStr.Append("Ammo");
	FName AmmoName = FName(*AmmoStr);

	int32& NewValue = MyItems.FindOrAdd(AmmoName);
	NewValue += RequiredAmmo;

	//TODO: 중복 할당 제거
	// FindOrAdd로 이미 참조를 얻었으므로 다시 할당할 필요 없음
	// MyItems[AmmoName] = NewValue; 라인 제거 가능
}

void ULSInventoryComp::UseItem(const FName& Input)
{
	//TODO: 아이템 존재 여부 확인 필요
	// 아이템이 없는데 사용하려고 할 때의 처리 필요
	if (!MyItems.Contains(Input) || MyItems[Input] <= 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Item %s not available"), *Input.ToString());
		return;
	}

	MyItems[Input]--;

	//TODO: 하드코딩된 아이템 처리를 데이터 테이블이나 맵으로 개선
	// 현재 "Kit"만 하드코딩되어 있는데, 다른 아이템들도 추가될 것을 고려하여
	// 아이템 타입별 처리 로직을 분리하는 것이 좋음
	if (Input == "Kit")
	{
		AActor* Owner = GetOwner();
		if (!Owner)
		{
			UE_LOG(LogTemp, Error, TEXT("Owner is null in UseItem"));
			return;
		}

		ALSPlayerCharacter* Character = Cast<ALSPlayerCharacter>(Owner);
		if (!Character)
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to cast owner to ALSPlayerCharacter"));
			return;
		}

		//TODO: 힐링 수치를 하드코딩하지 말고 설정 가능한 값으로 변경
		// 100이라는 값을 데이터 테이블이나 아이템 정보에서 가져오도록 개선
		Character->AddHealth(100);

		//TODO: 로그 레벨 조정 및 메시지 개선
		// Warning보다는 Log 레벨이 적절하고, 더 명확한 메시지 사용
		UE_LOG(LogTemp, Log, TEXT("Player used healing kit, restored 100 health"));
		Character->UpdateHealthBar(Character->GetCurrentHealth(), Character->GetMaxHealth());
	}
	//TODO: 다른 아이템 타입들에 대한 처리 추가
	// 예: 탄약, 버프 아이템, 장비 등
}

ECurrentWeapon ULSInventoryComp::ChangeWeaponNameToEnum(const FName& Input) 
{
	static const UEnum* EnumPtr = StaticEnum<ECurrentWeapon>();
	if (!EnumPtr)
	{
		return ECurrentWeapon::None;
	}

	int64 Value = EnumPtr->GetValueByName(Input);
	if (Value == INDEX_NONE)
	{
		return ECurrentWeapon::None;
	}

	return static_cast<ECurrentWeapon>(Value);
}

void ULSInventoryComp::EquipWeapon()
{
	AActor* Owner=GetOwner();
	if (!Owner)	return;

	ALSPlayerCharacter* Player=Cast<ALSPlayerCharacter>(Owner);
	if (!Player)	return;

	Player->SetCurrentWeapon(ChangeWeaponNameToEnum(MyWeaponName));
	Player->Equip();
	
}


