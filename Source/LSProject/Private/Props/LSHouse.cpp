// Fill out your copyright notice in the Description page of Project Settings.


#include "Props/LSHouse.h"

#include "Character/LSPlayerCharacter.h"
#include "Components/BoxComponent.h"
#include "Game/LSGameState.h"

ALSHouse::ALSHouse()
{
	//TODO: Tick 필요성 검토 필요
	// 집 오브젝트에서 Tick이 필요한 이유가 명확하지 않음
	// 불필요한 성능 오버헤드를 피하기 위해 false로 설정 권장
	PrimaryActorTick.bCanEverTick = true;

	StaticMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("StaticMeshComponent"));
	SetRootComponent(StaticMeshComponent);

	BoxCollision = CreateDefaultSubobject<UBoxComponent>(TEXT("BoxCollision"));
	BoxCollision->SetupAttachment(StaticMeshComponent);

	//TODO: 주석 처리된 코드 정리 필요
	// 사용하지 않는 코드는 제거하거나 필요한 경우 활성화하고 주석으로 이유 설명
	//BoxCollision->ComponentTags.Add(FName("Door"));
	//BoxCollision->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block);

	//TODO: 콜리전 설정 추가 필요
	// BoxCollision의 콜리전 채널, 응답 설정 등이 누락되어 있음
	// 어떤 오브젝트와 충돌할지 명확히 설정 필요
}

void ALSHouse::BeginPlay()
{
	Super::BeginPlay();

	//TODO: 델리게이트 바인딩 안전성 검증
	// BoxCollision이 유효한지 확인하고 바인딩
	if (BoxCollision)
	{
		BoxCollision->OnComponentBeginOverlap.AddDynamic(this, &ALSHouse::OnOverlapBegin);
		BoxCollision->OnComponentEndOverlap.AddDynamic(this, &ALSHouse::OnOverlapEnd);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("BoxCollision is null in %s"), *GetName());
	}
}

void ALSHouse::OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	//TODO: 플레이어 검증 로직 개선
	// 태그나 인터페이스를 사용하여 더 유연한 검증 방식 고려
	ALSPlayerCharacter* Character = Cast<ALSPlayerCharacter>(OtherActor);
	if (!Character)
	{
		return;
	}

	//TODO: GameState 접근 안전성 강화
	if (ALSGameState* GS = GetWorld()->GetGameState<ALSGameState>())
	{
		GS->SetDoorOverlapped(true);
		UE_LOG(LogTemp, Log, TEXT("Player entered house area"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get GameState in OnOverlapBegin"));
	}

	//TODO: 추가 상호작용 기능 고려
	// - 문 열기/닫기 애니메이션
	// - 상호작용 UI 표시
	// - 사운드 재생 등
}

void ALSHouse::OnOverlapEnd(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex)
{
	ALSPlayerCharacter* Character=Cast<ALSPlayerCharacter>(OtherActor);
	if (!Character)	return;
	
	if (ALSGameState* GS=GetWorld()->GetGameState<ALSGameState>())
	{
		GS->SetDoorOverlapped(false);
	}
}



