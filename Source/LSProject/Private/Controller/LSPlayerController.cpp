#include "Controller/LSPlayerController.h"
#include "EnhancedInputSubsystems.h"
#include "Widget/LSShopWidget.h"
#include "Widget/LSInventoryWidget.h"
#include "Blueprint/UserWidget.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Components/ProgressBar.h"
#include "Kismet/GameplayStatics.h"
#include "Camera/CameraActor.h"
#include "EngineUtils.h"

ALSPlayerController::ALSPlayerController() :
	InputMappingContext(nullptr),
	MoveAction(nullptr),
	LookAction(nullptr),
	JumpAction(nullptr),
	SprintAction(nullptr),
	AttackAction(nullptr),
	ReloadAction(nullptr),
	OpenShopAction(nullptr),
	OpenInvenAction(nullptr),
	RestoreFenceAction(nullptr),
	ShopWidgetClass(nullptr),
	ShopWidgetInstance(nullptr),
	InvenWidgetClass(nullptr),
	InvenWidgetInstance(nullptr),
	MainMenuWidgetClass(nullptr),
	InGameHUDWidgetClass(nullptr),
	GameOverWidgetClass(nullptr),
	GameClearWidgetClass(nullptr),
	MainMenuWidget(nullptr),
	InGameHUDWidget(nullptr),
	GameOverWidget(nullptr),
	GameClearWidget(nullptr)
{
}


void ALSPlayerController::BeginPlay()
{
	Super::BeginPlay();

	//TODO: 레벨 이름 하드코딩 개선 필요
	// 레벨 이름을 하드코딩하는 것보다는 enum이나 상수로 관리하는 것이 좋음
	// 또한 대소문자 구분 없는 비교 사용 권장
	const FString LevelName = UGameplayStatics::GetCurrentLevelName(this, true);
	if (LevelName.Equals(TEXT("LSStartMap"), ESearchCase::IgnoreCase))
	{
		ShowMainMenuWidget();
		return;
	}

	//TODO: 메인 맵이 아닌 다른 맵에서의 처리 고려
	// 현재는 StartMap이 아니면 무조건 MainMap으로 처리하는데,
	// 다른 맵들에 대한 처리도 필요할 수 있음
	SetupMainMapPlay();

	//TODO: Enhanced Input 설정 실패 시 처리 추가
	// 각 단계에서 실패할 수 있으므로 에러 처리 및 로그 추가 권장
	if (ULocalPlayer* LocalPlayer = GetLocalPlayer())
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem =
			LocalPlayer->GetSubsystem<UEnhancedInputLocalPlayerSubsystem>())
		{
			if (InputMappingContext)
			{
				Subsystem->AddMappingContext(InputMappingContext, 0);
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("InputMappingContext is not set"));
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to get EnhancedInputLocalPlayerSubsystem"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to get LocalPlayer"));
	}
}

void ALSPlayerController::ShowShopWidget()
{
	if (InvenWidgetInstance)
	{
		InvenWidgetInstance->RemoveFromParent();
		InvenWidgetInstance=nullptr;
	}
	
	if (!ShopWidgetInstance&& ShopWidgetClass)
	{
		ShopWidgetInstance = CreateWidget<ULSShopWidget>(this, ShopWidgetClass);
		if (ShopWidgetInstance)
		{
			ShopWidgetInstance->AddToViewport();
			FInputModeUIOnly InputMode;
			SetInputMode(InputMode);
			bShowMouseCursor = true;
		}
	}
}

void ALSPlayerController::HideShopWidget()
{
	if (ShopWidgetInstance)
	{
		ShopWidgetInstance->RemoveFromParent();
		FInputModeGameOnly InputMode;
		SetInputMode(InputMode);
		bShowMouseCursor = false;
		ShopWidgetInstance=nullptr;
		
		UE_LOG(LogTemp, Warning, TEXT("Hide SHOP WIDGET"));
	}
}

void ALSPlayerController::ShowInvenWidget()
{
	if (!InvenWidgetClass)	return;
	
	InvenWidgetInstance = CreateWidget<ULSInventoryWidget>( this, InvenWidgetClass);
	InvenWidgetInstance->AddToViewport();
	InvenWidgetInstance->SetVisibility(ESlateVisibility::Visible);
	FInputModeGameAndUI InputMode;
	InputMode.SetHideCursorDuringCapture(false);
	SetInputMode(InputMode);
	bShowMouseCursor = true;
}

void ALSPlayerController::HideInvenWidget()
{
	if (!InvenWidgetInstance)	return;
	
	InvenWidgetInstance->RemoveFromParent();
	FInputModeGameOnly InputMode;
	SetInputMode(InputMode);
	bShowMouseCursor = false;
	ShopWidgetInstance=nullptr;
}

void ALSPlayerController::GameStart()
{
	//TODO: 위젯 정리 로직 개선 필요
	// RemoveFromParent() 후 즉시 nullptr 할당하는 것보다는
	// 위젯의 유효성을 확인하고 안전하게 제거하는 로직 필요
	if (MainMenuWidget)
	{
		if (MainMenuWidget->IsInViewport())
		{
			MainMenuWidget->RemoveFromParent();
		}
		MainMenuWidget = nullptr;
	}

	FInputModeGameOnly InputMode;
	SetInputMode(InputMode);
	bShowMouseCursor = false;

	//TODO: 레벨 이름 하드코딩 개선 필요
	// "LSMainMap" 문자열을 상수나 설정 가능한 변수로 변경 권장
	UGameplayStatics::OpenLevel(this, FName(TEXT("LSMainMap")));
}

void ALSPlayerController::GameQuit()
{
	//TODO: 게임 종료 전 저장 로직 고려
	// 플레이어 진행 상황, 설정 등을 저장한 후 종료하는 것이 좋음
	// 또한 종료 확인 다이얼로그 표시 고려
	UKismetSystemLibrary::QuitGame(this, this, EQuitPreference::Quit, true);
}

void ALSPlayerController::ShowGameOverWidget()
{
	//TODO: 중복 호출 방지 로직은 좋지만, 리셋 기능도 필요할 수 있음
	// 게임 재시작 시 bGameOverShown을 false로 리셋하는 함수 필요
	if (bGameOverShown) return;
	bGameOverShown = true;

	HideHUDWidget();

	//TODO: 위젯 생성 실패 시 처리 강화
	if (!GameOverWidget && GameOverWidgetClass)
	{
		GameOverWidget = CreateWidget<UUserWidget>(this, GameOverWidgetClass);
		if (GameOverWidget)
		{
			GameOverWidget->AddToViewport(100);
			SetUIOnlyInput(GameOverWidget);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to create GameOverWidget"));
		}
	}
	else if (!GameOverWidgetClass)
	{
		UE_LOG(LogTemp, Error, TEXT("GameOverWidgetClass is not set"));
	}

	//TODO: 게임 오버 시 추가 처리 고려
	// - 게임 일시정지
	// - 배경음악 변경
	// - 통계 정보 표시 등
}
void ALSPlayerController::ShowGameClearWidget()
{
	if (bGameClearShown || bGameOverShown) return;
	bGameClearShown = true;

	HideHUDWidget();

	if (!GameClearWidget && GameClearWidgetClass)
	{
		GameClearWidget = CreateWidget<UUserWidget>(this, GameClearWidgetClass);
		if (GameClearWidget)
		{
			GameClearWidget->AddToViewport(100);
			SetUIOnlyInput(GameClearWidget);
		}
	}
}
void ALSPlayerController::HideHUDWidget()
{
	if (InGameHUDWidget && InGameHUDWidget->IsInViewport())
	{
		InGameHUDWidget->RemoveFromParent();
	}
}
UUserWidget* ALSPlayerController::GetHUDWidget() const
{
	return InGameHUDWidget;
}

void ALSPlayerController::ShowMainMenuWidget()
{
	LockToStartMapCamera();

	if (IsLocalController() && MainMenuWidgetClass)
	{
		MainMenuWidget = CreateWidget<UUserWidget>(this, MainMenuWidgetClass);
		if (MainMenuWidget)
		{
			MainMenuWidget->AddToViewport();

			FInputModeUIOnly InputMode;
			InputMode.SetWidgetToFocus(MainMenuWidget->TakeWidget());
			SetInputMode(InputMode);
			bShowMouseCursor = true;
		}
	}
}



void ALSPlayerController::LockToStartMapCamera() 
{
	if (TActorIterator<ACameraActor> It(GetWorld()); It) { SetViewTargetWithBlend(*It, 0.f); }
}

void ALSPlayerController::SetupMainMapPlay()
{
	if (InGameHUDWidgetClass && !InGameHUDWidget)
	{
		InGameHUDWidget = CreateWidget<UUserWidget>(this, InGameHUDWidgetClass);
		if (InGameHUDWidget)
		{
			InGameHUDWidget->AddToViewport();
		}
	}

	// 게임 입력 모드 전환
	FInputModeGameOnly InputMode;
	SetInputMode(InputMode);
	bShowMouseCursor = false;
}
void ALSPlayerController::SetUIOnlyInput(UUserWidget* Focus)
{
	FInputModeUIOnly Mode;
	if (Focus) Mode.SetWidgetToFocus(Focus->TakeWidget());
	Mode.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
	SetInputMode(Mode);
	bShowMouseCursor = true;
}
void ALSPlayerController::SetGameOnlyInput()
{
	FInputModeGameOnly Mode;
	SetInputMode(Mode);
	bShowMouseCursor = false; 
}

