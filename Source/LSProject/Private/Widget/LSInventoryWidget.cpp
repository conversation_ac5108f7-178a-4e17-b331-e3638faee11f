// Fill out your copyright notice in the Description page of Project Settings.


#include "Widget/LSInventoryWidget.h"
#include "Character/LSPlayerCharacter.h"
#include "Component/LSInventoryComp.h"
#include "Components/PanelWidget.h"
#include "Components/TextBlock.h"
#include "DataTable/LSInvenRow.h"
#include "Widget/LSInvenSlot.h"
#include "Components/Image.h"

void ULSInventoryWidget::NativeConstruct()
{
	Super::NativeConstruct();

	//TODO: 초기화 순서 최적화 고려
	// 빈 슬롯을 먼저 생성한 후 실제 아이템으로 채우는 순서가 맞는지 확인
	// 또한 각 초기화 함수가 실패할 경우에 대한 처리 필요
	LayOutEmptyInven();
	LayoutInInven();
	LayOutInWeapon();
	LayOutInUseItem();

	//TODO: 델리게이트 바인딩 안전성 검증
	// OnUpdateInvenUI가 이미 바인딩되어 있는지 확인하고 중복 바인딩 방지
	if (!OnUpdateInvenUI.IsBoundToObject(this))
	{
		OnUpdateInvenUI.AddDynamic(this, &ULSInventoryWidget::UpdateSlot);
	}
}

void ULSInventoryWidget::UpdateSlot()
{
	//TODO: 업데이트 최적화 필요
	// 전체 레이아웃을 다시 그리는 것보다는 변경된 부분만 업데이트하는 것이 효율적
	// 또한 업데이트 빈도 제한(throttling) 고려
	LayoutInInven();
	LayOutInWeapon();

	//TODO: UseItem 슬롯도 업데이트 필요한지 확인
	// LayOutInUseItem()도 호출해야 하는지 검토 필요
}

void ULSInventoryWidget::LayOutEmptyInven()
{
	EmptyContainer->ClearChildren();

	//빈 인벤 창
	if (!EmptySlotClass)	return;
	EmptySlot=CreateWidget<ULSInvenSlot>(this, EmptySlotClass);
	EmptySlot->SlotName=FName(TEXT("Empty"));
	EmptyContainer->AddChild(EmptySlot);
}

void ULSInventoryWidget::LayoutInInven()
{
	APawn* Pawn = GetOwningPlayerPawn();
	if (!Pawn)	return;
	
	ALSPlayerCharacter* Player=Cast<ALSPlayerCharacter>(Pawn);
	if (!Player)	return;

	ULSInventoryComp* InvenComp = Player->FindComponentByClass<ULSInventoryComp>();
	if (!InvenComp)	return;

	if (!InventoryTable)	return;

	InvenContainer->ClearChildren();
	
	const FString Context = TEXT("ULSInventoryWidget::NativeConstruct");

	//인벤 정리
	const TArray<FName> RowNames = InventoryTable->GetRowNames();
	for (const FName& RowName : RowNames)
	{
		const FLSInvenRow* Row = InventoryTable->FindRow<FLSInvenRow>(RowName, Context, true);
		if (!Row) continue;

		if (InvenComp->CountItem(Row->Name)<=0)	continue;
		
		if (InvenSlotClass)
		{
			ULSInvenSlot* InvenSlot = CreateWidget<ULSInvenSlot>(this, InvenSlotClass);

			InvenSlot->IconImage->SetBrushFromTexture(Row->Icon);
			
			FText NText=FText::FromName(Row->Name);
			InvenSlot->NameText->SetText(NText);

			FString CString=FString::Printf(TEXT("X %d"),InvenComp->CountItem(Row->Name));
			InvenSlot->CountText->SetText(FText::FromString(CString));

			InvenSlot->SlotName=FName(TEXT("Inventory"));
			
			InvenSlot->SetType(Row->Category);
			
			InvenContainer->AddChild(InvenSlot);
		}
	}
	
}

void ULSInventoryWidget::LayOutInWeapon()
{
	APawn* Pawn = GetOwningPlayerPawn();
	if (!Pawn)	return;
	
	ALSPlayerCharacter* Player=Cast<ALSPlayerCharacter>(Pawn);
	if (!Player)	return;

	ULSInventoryComp* InvenComp = Player->FindComponentByClass<ULSInventoryComp>();
	if (!InvenComp)	return;

	if (!InventoryTable)	return;
	
	WeaponContainer->ClearChildren();	
	
	const FString Context = TEXT("ULSInventoryWidget::NativeConstruct");

	//인벤 정리
	const TArray<FName> RowNames = InventoryTable->GetRowNames();
	
	//내무기
	if (!WeaponSlotClass)	return;
	WeaponSlot=CreateWidget<ULSInvenSlot>(this, WeaponSlotClass);
	WeaponSlot->SlotName=FName(TEXT("Weapon"));
	WeaponContainer->AddChild(WeaponSlot);

	for (const FName& RowName : RowNames)
	{
		const FLSInvenRow* Row = InventoryTable->FindRow<FLSInvenRow>(RowName, Context, true);
		if (!Row) continue;

		//UE_LOG(LogTemp, Warning, TEXT("MyWeapon %s"),*InvenComp->GetMyWeapon().ToString());
		
		if (InvenComp->GetMyWeapon()==Row->Name)																					
		{
			WeaponSlot->SetType(Row->Category);
			WeaponSlot->IconImage->SetBrushFromTexture(Row->Icon);
		}	
	}
}

void ULSInventoryWidget::LayOutInUseItem()
{
	UseItemContainer->ClearChildren();	
	if (!EmptySlotClass)	return;
	UseItemSlot=CreateWidget<ULSInvenSlot>(this, EmptySlotClass);
	UseItemSlot->SlotName=FName(TEXT("UseItem"));
	UseItemContainer->AddChild(UseItemSlot);
	
}
