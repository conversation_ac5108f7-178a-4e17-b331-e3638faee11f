// Fill out your copyright notice in the Description page of Project Settings.

#include "Widget/LSShopWidget.h"

#include "Character/LSPlayerCharacter.h"
#include "Components/Button.h"
#include "DataTable/LSShopItemRow.h"
#include "Widget/LSBuyButtonWidget.h"
#include "Components/TextBlock.h"
#include "Component/LSShopComp.h"
#include "Components/Image.h"
#include "Controller/LSPlayerController.h"
#include "Game/LSPlayerState.h"

void ULSShopWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ALSPlayerState* PS = GetOwningPlayerState<ALSPlayerState>(false);
	if (PS)
	{
		CoinText->SetText(FText::AsNumber(PS->GetCoin()));
	}
	
	if (APawn* Pawn = GetOwningPlayerPawn())
	{
		if (ALSPlayerCharacter* Player=Cast<ALSPlayerCharacter>(Pawn))
		{
			ULSShopComp* ShopComp = Player->FindComponentByClass<ULSShopComp>();
			if (ShopComp)
			{
				ShopComp->OnShopNotEnoughMoney.AddDynamic(this,&ULSShopWidget::ShowInsufficientMoneyText);
			}
		}

		NotEnoughMoneyText->SetVisibility(ESlateVisibility::Hidden);
	}

	SetIsFocusable(true);
}

void ULSShopWidget::NativeConstruct()
{
	Super::NativeConstruct();

	SetKeyboardFocus();
	
	if (!WeaponContainer||!AttachmentContainer||!ItemContainer)	return;
	
	const FString Context = TEXT("ULSStoreWidget::NativeConstruct");
	
	const TArray<FName> RowNames = ShopItemTable->GetRowNames();
	for (const FName& RowName : RowNames)
	{
		const FLSShopItemRow* Row = ShopItemTable->FindRow<FLSShopItemRow>(RowName, Context, true);
		if (!Row) continue;

		ULSBuyButtonWidget* Btn = CreateWidget<ULSBuyButtonWidget>(this, BuyButtonWidgetClass);
		if (!Btn) continue;

		FText NText=FText::FromName(Row->Name);
		Btn->NameText->SetText(NText);

		FString PString=FString::Printf(TEXT("%d$"),Row->Price);
		Btn->PriceText->SetText(FText::FromString(PString));
		
		FString NString=FString::Printf(TEXT("X %d"),Row->Number);
		Btn->NumText->SetText(FText::FromString(NString));

		Btn->IconImage->SetBrushFromTexture(Row->Icon);
		
		if (Row->ShopCategory==FName(TEXT("Weapon")))
		{
			WeaponContainer->AddChild(Btn);
		}
		else if (Row->ShopCategory==FName(TEXT("Attachment")))
		{
			AttachmentContainer->AddChild(Btn);
		}
		else if (Row->ShopCategory==FName(TEXT("Item")))
		{
			ItemContainer->AddChild(Btn);
		}
		

		Btn->OnBuyClicked.AddUniqueDynamic(this, &ULSShopWidget::HandleBuyClicked);
	}
}


FReply ULSShopWidget::NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent)
{
	//TODO: 키 바인딩을 하드코딩하지 말고 설정 가능하게 개선
	// F키를 하드코딩하는 것보다는 Input Action을 사용하거나 설정에서 변경 가능하도록 개선
	if (InKeyEvent.GetKey() == EKeys::F)
	{
		//TODO: PlayerController 접근 방식 개선
		// GetWorld()->GetFirstPlayerController()보다는 GetOwningPlayer() 사용 권장
		// 멀티플레이어 환경에서 더 안전함
		if (ALSPlayerController* PC = Cast<ALSPlayerController>(GetOwningPlayer()))
		{
			PC->HideShopWidget();
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to get PlayerController in shop widget"));
		}

		//TODO: 로그 레벨 조정 및 메시지 개선
		// Warning보다는 Log 레벨이 적절하고, 더 의미있는 메시지 사용
		UE_LOG(LogTemp, Log, TEXT("Shop widget closed by F key"));

		return FReply::Handled();
	}

	return Super::NativeOnKeyDown(InGeometry, InKeyEvent);
}

ULSShopWidget::ULSShopWidget(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	//TODO: 초기화 값들을 설정 가능한 변수로 변경
	// nullptr과 1.0f 같은 하드코딩된 값들을 UPROPERTY로 노출하여 조정 가능하도록 개선
	ShopItemTable = nullptr;
	DurationDisappear = 1.0f;
	SetIsFocusable(true);

	//TODO: 추가 초기화 로직 고려
	// - 기본 포커스 설정
	// - 애니메이션 초기화
	// - 사운드 설정 등
}

void ULSShopWidget::HandleBuyClicked(const FName& Name)
{
	//TODO: 구매 처리 로직 개선 필요
	// 현재 여러 단계의 캐스팅과 컴포넌트 검색이 있어 실패 지점이 많음
	// 각 단계별로 적절한 에러 처리와 로그 추가 필요

	if (APawn* Pawn = GetOwningPlayerPawn())
	{
		if (ALSPlayerCharacter* Player = Cast<ALSPlayerCharacter>(Pawn))
		{
			ULSShopComp* ShopComp = Player->FindComponentByClass<ULSShopComp>();
			if (ShopComp)
			{
				//TODO: 구매 결과 확인 및 처리
				// BuyItem의 반환값을 확인하여 구매 성공/실패에 따른 UI 피드백 제공
				ShopComp->BuyItem(Name);

				//TODO: UI 업데이트 로직 개선
				// PlayerState 접근 실패 시에도 UI가 올바르게 업데이트되도록 개선
				if (ALSPlayerState* PS = GetOwningPlayerState<ALSPlayerState>(false))
				{
					if (CoinText)
					{
						CoinText->SetText(FText::AsNumber(PS->GetCoin()));
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("CoinText widget is null"));
					}
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("Failed to get PlayerState for coin update"));
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("ShopComp not found on player"));
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to cast pawn to ALSPlayerCharacter"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("No owning player pawn found"));
	}

	//TODO: 구매 후 추가 처리 고려
	// - 구매 사운드 재생
	// - 구매 애니메이션 표시
	// - 인벤토리 UI 업데이트 알림 등
}

void ULSShopWidget::ShowInsufficientMoneyText()
{
	NotEnoughMoneyText->SetVisibility(ESlateVisibility::Visible);

	GetWorld()->GetTimerManager().SetTimer(
		HideNotEnoughMoneyTextTimer,
		this,
		&ULSShopWidget::HideInsufficientMoneyText,
		DurationDisappear,
		false
		);
}

void ULSShopWidget::HideInsufficientMoneyText()
{
	NotEnoughMoneyText->SetVisibility(ESlateVisibility::Hidden);
}

