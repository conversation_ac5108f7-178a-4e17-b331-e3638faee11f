// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "LSShopItemRow.generated.h"

/**
 * TODO: 클래스 문서화 개선 필요
 * 상점 아이템 데이터를 정의하는 구조체임을 명시하고
 * 각 필드의 용도와 제약사항을 설명하는 주석 추가 권장
 */
USTRUCT(BlueprintType)
struct FLSShopItemRow : public FTableRowBase
{
	GENERATED_BODY()

public:
	//TODO: 기본값 설정 방식 개선
	// TEXT("None") 대신 static const FName을 사용하여 메모리 효율성 개선
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Item Name"))
	FName Name = TEXT("None");

	//TODO: 가격 유효성 검증 추가
	// 음수 가격을 방지하기 위한 meta 태그 추가 권장: meta = (ClampMin = "0")
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Price", ClampMin = "0"))
	int32 Price = 0;

	//TODO: 카테고리 enum 사용 고려
	// FName 대신 enum을 사용하여 오타 방지 및 타입 안전성 확보
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Shop Category"))
	FName ShopCategory = TEXT("None");

	//TODO: 아이콘 필수 여부 검토
	// 아이콘이 필수인지 선택사항인지 명확히 하고, 기본 아이콘 제공 고려
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Item Icon"))
	UTexture2D* Icon = nullptr;

	//TODO: 수량 필드명 개선 및 유효성 검증
	// "Number"보다는 "Quantity" 또는 "Amount"가 더 명확
	// 음수 방지를 위한 ClampMin 추가 권장
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Quantity", ClampMin = "1"))
	int32 Number = 1;  // TODO: 기본값을 1로 변경 (0개 판매는 의미가 없음)

	//TODO: 추가 필드 고려
	// - 아이템 설명 (FText Description)
	// - 아이템 타입 (무기, 소모품, 장비 등)
	// - 구매 제한 (일일 구매 한도 등)
	// - 할인율 정보 등
};
