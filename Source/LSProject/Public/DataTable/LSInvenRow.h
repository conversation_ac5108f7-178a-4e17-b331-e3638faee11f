// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LSInvenRow.generated.h"

/**
 * TODO: 클래스 문서화 개선 필요
 * 인벤토리 아이템 정보를 정의하는 구조체임을 명시하고
 * ShopItemRow와의 차이점 및 사용 목적을 명확히 설명
 */
USTRUCT(BlueprintType)
struct FLSInvenRow : public FTableRowBase
{
	GENERATED_BODY()
public:
	//TODO: ShopItemRow와 중복되는 필드들 통합 고려
	// Name, Icon 등이 중복되므로 공통 베이스 구조체 생성 검토
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Item Name"))
	FName Name = TEXT("None");

	//TODO: 아이콘 관리 일관성 확보
	// ShopItemRow와 동일한 방식으로 아이콘 처리
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Item Icon"))
	UTexture2D* Icon = nullptr;

	//TODO: 카테고리 시스템 통합 필요
	// ShopItemRow의 ShopCategory와 통합하거나 enum 사용 고려
	UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (DisplayName = "Item Category"))
	FName Category = TEXT("None");

	//TODO: 인벤토리 전용 필드 추가 고려
	// - 스택 가능 여부 (bool bStackable)
	// - 최대 스택 수량 (int32 MaxStackSize)
	// - 아이템 등급/품질 (enum ItemRarity)
	// - 사용 가능 여부 (bool bUsable)
	// - 장착 가능 여부 (bool bEquippable) 등
};
