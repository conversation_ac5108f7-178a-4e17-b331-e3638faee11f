// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "LSInvenSlotInfo.generated.h"

/**
 * TODO: 클래스 문서화 개선 필요
 * 인벤토리 슬롯의 UI 정보를 관리하는 클래스임을 명시하고
 * 사용 목적과 생명주기를 설명하는 주석 추가
 */

class UTextBlock;
class UImage;

UCLASS()
class LSPROJECT_API ULSInvenSlotInfo : public UObject
{
	GENERATED_BODY()

public:
	//TODO: 위젯 포인터들의 생명주기 관리 개선 필요
	// 위젯들이 삭제될 때 nullptr로 설정되는지 확인하고
	// WeakPtr 사용 고려하여 댕글링 포인터 방지
	UPROPERTY()
	TObjectPtr<UTextBlock> NameText = nullptr;

	UPROPERTY()
	TObjectPtr<UTextBlock> CountText = nullptr;

	UPROPERTY()
	TObjectPtr<UImage> IconImage = nullptr;

	//TODO: Type 필드의 용도 명확화 필요
	// 아이템 타입인지, 슬롯 타입인지 명확히 하고
	// enum 사용을 고려하여 타입 안전성 확보
	UPROPERTY()
	FName Type = TEXT("None");

	//TODO: 추가 기능 고려
	// - 슬롯 상태 관리 (비어있음, 선택됨, 비활성화 등)
	// - 위젯 업데이트 함수들 (UpdateName, UpdateCount, UpdateIcon 등)
	// - 슬롯 초기화/정리 함수
	// - 드래그&드롭 지원을 위한 데이터
};

//TODO: 이 클래스가 정말 UObject를 상속해야 하는지 검토
// 단순한 데이터 홀더라면 구조체(USTRUCT)로도 충분할 수 있음
// UObject 상속 시 GC 오버헤드 발생
